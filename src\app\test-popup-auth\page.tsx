"use client";

import { usePopupAuth } from "@/hooks/usePopupAuth";
import { useAuth } from "@/lib/auth-context";
import { createClient } from "@/lib/supabase/client";
import { useState } from "react";

export default function TestPopupAuth() {
  const { user } = useAuth();
  const { signIn, signInWithRetry, isLoading, error, clearError, canRetry } = usePopupAuth();
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handlePopupAuth = async () => {
    addTestResult("Starting popup authentication test...");
    clearError();
    
    try {
      const result = await signIn("google", {
        width: 500,
        height: 600,
        timeout: 300000
      });

      if (result.success) {
        addTestResult("✅ Popup authentication successful!");
        addTestResult("🔍 Checking session state...");
        
        // Verify session was actually created
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          addTestResult("✅ Session verified - user is logged in");
          addTestResult(`👤 User: ${session.user.email}`);
        } else {
          addTestResult("❌ No session found despite success result");
        }
      } else {
        addTestResult(`❌ Popup authentication failed: ${result.error}`);
        addTestResult(`🔍 Error code: ${result.errorCode}`);
        
        // Check if user is actually logged in despite the error
        addTestResult("🔍 Checking actual session state...");
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          addTestResult("✅ User is actually logged in despite error!");
          addTestResult(`👤 User: ${session.user.email}`);
        } else {
          addTestResult("❌ User is not logged in");
        }
      }
    } catch (err) {
      addTestResult(`💥 Exception during authentication: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handlePopupAuthWithRetry = async () => {
    addTestResult("Starting popup authentication with retry test...");
    clearError();
    
    try {
      const result = await signInWithRetry("google", {
        width: 500,
        height: 600,
        timeout: 300000,
        retryAttempts: 2
      });

      if (result.success) {
        addTestResult("✅ Popup authentication with retry successful!");
      } else {
        addTestResult(`❌ Popup authentication with retry failed: ${result.error}`);
      }
    } catch (err) {
      addTestResult(`💥 Exception during retry authentication: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const handleLogout = async () => {
    addTestResult("Logging out...");
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      addTestResult("✅ Logged out successfully");
    } catch (err) {
      addTestResult(`❌ Logout failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          Popup Authentication Test Page
        </h1>
        
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Current Authentication Status</h2>
          {user ? (
            <div className="text-green-400">
              <p>✅ Logged in as: {user.email}</p>
              <p>🆔 User ID: {user.id}</p>
            </div>
          ) : (
            <p className="text-red-400">❌ Not logged in</p>
          )}
        </div>

        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={handlePopupAuth}
              disabled={isLoading}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              {isLoading ? "Testing..." : "Test Popup Auth"}
            </button>
            
            <button
              onClick={handlePopupAuthWithRetry}
              disabled={isLoading}
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg transition-colors"
            >
              {isLoading ? "Testing..." : "Test Popup Auth with Retry"}
            </button>
            
            {user && (
              <button
                onClick={handleLogout}
                className="px-6 py-3 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              >
                Logout
              </button>
            )}
            
            <button
              onClick={clearResults}
              className="px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
            >
              Clear Results
            </button>
          </div>
          
          {error && (
            <div className="mt-4 p-4 bg-red-900 border border-red-600 rounded-lg">
              <p className="text-red-200">Error: {error}</p>
              {canRetry && (
                <button
                  onClick={clearError}
                  className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm"
                >
                  Clear Error
                </button>
              )}
            </div>
          )}
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Results</h2>
          <div className="bg-gray-900 rounded p-4 max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-400 italic">No test results yet. Click a test button to start.</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Instructions</h2>
          <div className="text-gray-300 space-y-2">
            <p>1. <strong>Test Popup Auth:</strong> Tests the basic popup authentication flow</p>
            <p>2. <strong>Test Popup Auth with Retry:</strong> Tests authentication with retry logic</p>
            <p>3. <strong>Watch for popup closing:</strong> The main fix is that popups should close automatically after authentication</p>
            <p>4. <strong>Check console:</strong> Look for "Popup requested to be closed" and "Received close confirmation" messages</p>
            <p>5. <strong>Production vs Development:</strong> This fix specifically addresses popup closing issues in production</p>
          </div>
        </div>
      </div>
    </div>
  );
}
